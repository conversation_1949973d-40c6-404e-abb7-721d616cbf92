/**
 * 模式感知数据生成器
 * 🎯 核心价值：根据显示模式按需生成优化的数据结构，提升性能
 * 📦 功能范围：坐标模式、数值模式、颜色模式的专用数据生成
 * ⚡ 性能优化：减少不必要的数据生成，降低内存使用
 */

import { SpecialCoordinateService } from '@/lib/services/SpecialCoordinateService';
import { ColorMappingService } from '@/lib/services/ColorMappingService';
import { generateMatrixData } from '@/lib/utils/matrixUtils';
import { GRID_CONSTANTS } from '@/stores/constants/grid';
import type {
  DataMode,
  CoordinateOnlyData,
  BasicRenderData,
  FullRenderData,
  CoordinateOnlyDataSet,
  BasicRenderDataSet,
  FullRenderDataSet,
  GridDataSet,
  DataGenerationConfig,
  SpecialCharacterInfo
} from '@/lib/types/grid-data-modes';

/**
 * 模式感知数据生成器类
 */
export class ModeAwareDataGenerator {
  private gridSize: number;
  private centerOffset: number;

  constructor(gridSize: number = GRID_CONSTANTS.GRID_SIZE) {
    this.gridSize = gridSize;
    this.centerOffset = Math.floor(gridSize / 2);
  }

  /**
   * 根据配置生成对应模式的数据
   */
  generateData(config: DataGenerationConfig): GridDataSet {
    const startTime = performance.now();

    let result: GridDataSet;

    switch (config.mode) {
      case DataMode.COORDINATE_ONLY:
        result = this.generateCoordinateOnlyData(config);
        break;
      case DataMode.BASIC_RENDER:
        result = this.generateBasicRenderData(config);
        break;
      case DataMode.FULL_RENDER:
        result = this.generateFullRenderData(config);
        break;
      default:
        throw new Error(`不支持的数据模式: ${config.mode}`);
    }

    const generationTime = performance.now() - startTime;
    
    process.env.NODE_ENV === 'development' && console.log(`🚀 [ModeAwareGenerator] ${config.mode} 数据生成完成:`, {
      mode: config.mode,
      cellCount: result.totalCells,
      generationTime: `${generationTime.toFixed(2)}ms`,
      memoryEstimate: this.estimateMemoryUsage(config.mode)
    });

    return result;
  }

  /**
   * 生成仅坐标数据（最轻量级）
   */
  private generateCoordinateOnlyData(config: DataGenerationConfig): CoordinateOnlyDataSet {
    const cells: CoordinateOnlyData[][] = [];
    const specialCharacters = new Map<string, SpecialCharacterInfo>();
    let totalCells = 0;

    for (let row = 0; row < this.gridSize; row++) {
      const gridRow: CoordinateOnlyData[] = [];

      for (let col = 0; col < this.gridSize; col++) {
        const x = col - this.centerOffset;
        const y = this.centerOffset - row;
        const cellId = row * this.gridSize + col;
        const index = cellId;

        // 检查特殊字符
        let specialChar: SpecialCharacterInfo | undefined;
        if (config.enableSpecialCharacters) {
          const character = SpecialCoordinateService.getCharacterForCoordinate(x, y);
          if (character) {
            specialChar = { character, isSpecial: true };
            specialCharacters.set(`${x},${y}`, specialChar);
          }
        }

        const cellData: CoordinateOnlyData = {
          id: `cell-${cellId}`,
          row,
          col,
          x,
          y,
          index,
          specialChar,
          isActive: true // 坐标模式下所有格子都激活
        };

        gridRow.push(cellData);
        totalCells++;
      }

      cells.push(gridRow);
    }

    return {
      mode: DataMode.COORDINATE_ONLY,
      cells,
      specialCharacters,
      totalCells
    };
  }

  /**
   * 生成基础渲染数据（包含颜色映射值）
   */
  private generateBasicRenderData(config: DataGenerationConfig): BasicRenderDataSet {
    const cells: BasicRenderData[][] = [];
    const coordinateMap = new Map<string, { colorMappingValue: number; level: number }>();
    const specialCharacters = new Map<string, SpecialCharacterInfo>();
    let totalCells = 0;

    // 生成矩阵数据以获取颜色映射信息
    const matrixData = generateMatrixData(false, false);

    for (let row = 0; row < this.gridSize; row++) {
      const gridRow: BasicRenderData[] = [];

      for (let col = 0; col < this.gridSize; col++) {
        const x = col - this.centerOffset;
        const y = this.centerOffset - row;
        const cellId = row * this.gridSize + col;
        const index = cellId;
        const coordKey = `${x},${y}`;

        // 检查特殊字符
        let specialChar: SpecialCharacterInfo | undefined;
        if (config.enableSpecialCharacters) {
          const character = SpecialCoordinateService.getCharacterForCoordinate(x, y);
          if (character) {
            specialChar = { character, isSpecial: true };
            specialCharacters.set(coordKey, specialChar);
          }
        }

        // 获取颜色映射信息
        let colorMappingValue = 0;
        let level = 1;
        let color = 'transparent';

        const dataPoints = matrixData.byCoordinate.get(coordKey);
        if (dataPoints && dataPoints.length > 0) {
          const firstDataPoint = dataPoints[0];
          colorMappingValue = ColorMappingService.getNumericValue(firstDataPoint.color, firstDataPoint.level);
          level = firstDataPoint.level;
          color = ColorMappingService.getColorValue(firstDataPoint.color);
          
          coordinateMap.set(coordKey, { colorMappingValue, level });
        } else if (specialChar) {
          colorMappingValue = SpecialCoordinateService.getNumericValueForCharacter(specialChar.character);
          color = '#000000'; // 黑色
        }

        const cellData: BasicRenderData = {
          id: `cell-${cellId}`,
          row,
          col,
          x,
          y,
          index,
          colorMappingValue,
          level,
          color,
          specialChar,
          isActive: true
        };

        gridRow.push(cellData);
        totalCells++;
      }

      cells.push(gridRow);
    }

    return {
      mode: DataMode.BASIC_RENDER,
      cells,
      coordinateMap,
      specialCharacters,
      totalCells
    };
  }

  /**
   * 生成完整渲染数据（包含所有信息）
   */
  private generateFullRenderData(config: DataGenerationConfig): FullRenderDataSet {
    const cells: FullRenderData[][] = [];
    const coordinateMap = new Map<string, { color: any; level: number; group: any }>();
    const specialCharacters = new Map<string, SpecialCharacterInfo>();
    let totalCells = 0;

    // 生成完整的矩阵数据
    const matrixData = generateMatrixData(true, false);

    for (let row = 0; row < this.gridSize; row++) {
      const gridRow: FullRenderData[] = [];

      for (let col = 0; col < this.gridSize; col++) {
        const x = col - this.centerOffset;
        const y = this.centerOffset - row;
        const cellId = row * this.gridSize + col;
        const index = cellId;
        const coordKey = `${x},${y}`;
        const number = cellId + 1;

        // 检查特殊字符
        let specialChar: SpecialCharacterInfo | undefined;
        if (config.enableSpecialCharacters) {
          const character = SpecialCoordinateService.getCharacterForCoordinate(x, y);
          if (character) {
            specialChar = { character, isSpecial: true };
            specialCharacters.set(coordKey, specialChar);
          }
        }

        // 获取完整的数据信息
        let colorMappingValue = 0;
        let level = 1;
        let color = 'transparent';
        let colorType: any = undefined;
        let group: any = null;

        const dataPoints = matrixData.byCoordinate.get(coordKey);
        if (dataPoints && dataPoints.length > 0) {
          const firstDataPoint = dataPoints[0];
          colorMappingValue = ColorMappingService.getNumericValue(firstDataPoint.color, firstDataPoint.level);
          level = firstDataPoint.level;
          color = ColorMappingService.getColorValue(firstDataPoint.color);
          colorType = firstDataPoint.color;
          group = firstDataPoint.group;
          
          coordinateMap.set(coordKey, { color: colorType, level, group });
        } else if (specialChar) {
          colorMappingValue = SpecialCoordinateService.getNumericValueForCharacter(specialChar.character);
          color = '#000000'; // 黑色
        }

        const cellData: FullRenderData = {
          id: `cell-${cellId}`,
          row,
          col,
          x,
          y,
          index,
          color,
          colorMappingValue,
          colorType,
          level,
          group,
          specialChar,
          isActive: true,
          number
        };

        gridRow.push(cellData);
        totalCells++;
      }

      cells.push(gridRow);
    }

    return {
      mode: DataMode.FULL_RENDER,
      cells,
      coordinateMap,
      specialCharacters,
      matrixData,
      totalCells
    };
  }

  /**
   * 估算内存使用量
   */
  private estimateMemoryUsage(mode: DataMode): string {
    const baseCellSize = 200; // 基础单元格大小（字节）
    const totalCells = this.gridSize * this.gridSize;
    
    let multiplier = 1;
    switch (mode) {
      case DataMode.COORDINATE_ONLY:
        multiplier = 0.3;
        break;
      case DataMode.BASIC_RENDER:
        multiplier = 0.6;
        break;
      case DataMode.FULL_RENDER:
        multiplier = 1.0;
        break;
    }

    const estimatedBytes = totalCells * baseCellSize * multiplier;
    return `${(estimatedBytes / 1024).toFixed(1)}KB`;
  }

  /**
   * 获取推荐的数据模式
   */
  static getRecommendedMode(displayMode: string): DataMode {
    switch (displayMode) {
      case 'coordinates':
        return DataMode.COORDINATE_ONLY;
      case 'value':
        return DataMode.BASIC_RENDER;
      case 'color':
        return DataMode.FULL_RENDER;
      default:
        return DataMode.COORDINATE_ONLY;
    }
  }

  /**
   * 检查是否可以升级数据模式
   */
  static canUpgradeMode(from: DataMode, to: DataMode): boolean {
    const modeOrder = [DataMode.COORDINATE_ONLY, DataMode.BASIC_RENDER, DataMode.FULL_RENDER];
    const fromIndex = modeOrder.indexOf(from);
    const toIndex = modeOrder.indexOf(to);
    return fromIndex < toIndex;
  }
}

// 创建全局实例
export const globalModeAwareGenerator = new ModeAwareDataGenerator();
