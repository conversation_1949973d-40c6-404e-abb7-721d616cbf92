/**
 * 矩阵计算工具函数
 * 🎯 职责：矩阵数据生成、坐标计算、变换规则等核心计算逻辑
 * 📦 重构来源：从 basicDataStore.ts 中提取的计算函数
 * ✅ 纯函数：无副作用，便于测试和复用
 */

import type { 
  BasicColorType, 
  GroupType, 
  MatrixDataPoint, 
  MatrixData, 
  CellData 
} from '@/lib/types/matrix';
import { 
  GROUP_A_DATA, 
  GROUP_OFFSET_CONFIGS, 
  AVAILABLE_LEVELS,
  GRID_DIMENSIONS,
  GRID_CENTER 
} from '@/stores/constants/matrix';

/**
 * 验证坐标是否在有效范围内
 * @param x X坐标
 * @param y Y坐标
 * @returns 是否有效
 */
export const isValidCoordinate = (x: number, y: number): boolean => {
  return x >= -16 && x <= 16 && y >= -16 && y <= 16;
};

/**
 * 生成变换规则名称
 * @param color 颜色
 * @param level 级别
 * @param offsetX X偏移
 * @param offsetY Y偏移
 * @returns 规则名称
 */
export const generateTransformRule = (
  color: BasicColorType,
  level: 1 | 2 | 3 | 4,
  offsetX: number,
  offsetY: number
): string => {
  if (offsetX === 0 && offsetY === 0) {
    return `${color}_level${level}_identity`;
  }
  return `${color}_level${level}_offset_${offsetX}_${offsetY}`;
};

/**
 * 统一的组坐标计算函数 - 支持A到M所有组类型
 * @param group 组类型（A-M）
 * @param color 颜色类型
 * @param level 级别
 * @returns 矩阵数据点数组
 */
export const calculateGroupCoordinates = (
  group: GroupType,
  color: BasicColorType,
  level: 1 | 2 | 3 | 4
): MatrixDataPoint[] => {
  // 获取A组基础数据
  const colorData = GROUP_A_DATA[color];
  if (!colorData) return [];

  const aGroupData = colorData[level as keyof typeof colorData] as readonly [number, number][] | undefined;
  if (!aGroupData) return [];

  // 获取组配置
  const groupConfig = GROUP_OFFSET_CONFIGS[group];
  if (!groupConfig) {
    process.env.NODE_ENV === 'development' && console.warn(`未找到组 ${group} 的配置，请在 GROUP_OFFSET_CONFIGS 中添加`);
    return [];
  }

  const results: MatrixDataPoint[] = [];

  aGroupData.forEach(([x, y]: [number, number]) => {
    // 确定偏移量
    let offsetX: number, offsetY: number;

    // 优先使用特定级别和颜色的偏移配置
    const levelSpecificOffset = groupConfig.levelOffsets?.[level]?.[color];
    if (levelSpecificOffset) {
      [offsetX, offsetY] = levelSpecificOffset;
    }
    // 其次使用level1的特殊偏移配置
    else if (level === 1 && groupConfig.level1Offsets?.[color]) {
      [offsetX, offsetY] = groupConfig.level1Offsets[color];
    }
    // 最后使用默认偏移
    else {
      [offsetX, offsetY] = groupConfig.defaultOffset;
    }

    // 应用偏移
    const newX = x + offsetX;
    const newY = y + offsetY;

    // 生成变换规则名称
    const transformRule = generateTransformRule(color, level, offsetX, offsetY);

    // 只保留有效坐标
    if (isValidCoordinate(newX, newY)) {
      results.push({
        coords: [newX, newY] as [number, number],
        group,
        level,
        color,
        transformRule
      });
    }
  });

  return results;
};

// 数据生成缓存
let _cachedMatrixData: MatrixData | null = null;
let _cacheTimestamp: number = 0;
const CACHE_DURATION = 5000; // 5秒缓存

/**
 * 生成完整的矩阵数据 - 包含A到M所有组的数据点
 * @param ensureVisibility 是否确保生成的数据与默认可见性配置匹配
 * @param forceRegenerate 是否强制重新生成（忽略缓存）
 * @returns 完整的矩阵数据结构
 */
export const generateMatrixData = (ensureVisibility: boolean = false, forceRegenerate: boolean = false): MatrixData => {
  const startTime = performance.now();

  // 检查缓存
  const now = Date.now();
  if (!forceRegenerate && _cachedMatrixData && (now - _cacheTimestamp) < CACHE_DURATION) {
    process.env.NODE_ENV === 'development' && console.log(`🚀 [MatrixUtils] 使用缓存数据，耗时: ${(performance.now() - startTime).toFixed(2)}ms`);
    return _cachedMatrixData;
  }

  process.env.NODE_ENV === 'development' && console.log('🔄 [MatrixUtils] 开始生成矩阵数据');

  const matrixData: MatrixData = {
    byCoordinate: new Map(),
    byGroup: {} as Record<GroupType, MatrixDataPoint[]>,
    byColor: {} as Record<BasicColorType, MatrixDataPoint[]>,
    byLevel: {} as Record<1 | 2 | 3 | 4, MatrixDataPoint[]>,
  };

  // 初始化索引结构
  const groups: GroupType[] = ["A", "B", "C", "D", "E", "F", "G", "H", "I", "J", "K", "L", "M"];
  const colors: BasicColorType[] = ["black", "red", "orange", "yellow", "green", "cyan", "blue", "purple", "pink"];
  const levels: (1 | 2 | 3 | 4)[] = [1, 2, 3, 4];

  // 预分配数组以提高性能
  groups.forEach(group => {
    matrixData.byGroup[group] = [];
  });

  colors.forEach(color => {
    matrixData.byColor[color] = [];
  });

  levels.forEach(level => {
    matrixData.byLevel[level] = [];
  });

  let totalDataPoints = 0;
  const groupProcessStartTime = performance.now();

  // 生成所有组的数据点
  groups.forEach(group => {
    colors.forEach(color => {
      const availableLevels = AVAILABLE_LEVELS[color];
      availableLevels.forEach(level => {
        const dataPoints = calculateGroupCoordinates(group, color, level as 1 | 2 | 3 | 4);

        if (dataPoints.length === 0) return; // 早期退出，避免无用处理

        totalDataPoints += dataPoints.length;

        dataPoints.forEach(point => {
          // 添加到各种索引中
          matrixData.byGroup[group].push(point);
          matrixData.byColor[color].push(point);
          matrixData.byLevel[point.level].push(point);

          // 添加到坐标索引
          const coordKey = `${point.coords[0]},${point.coords[1]}`;
          if (!matrixData.byCoordinate.has(coordKey)) {
            matrixData.byCoordinate.set(coordKey, []);
          }
          matrixData.byCoordinate.get(coordKey)!.push(point);
        });
      });
    });
  });

  const groupProcessTime = performance.now() - groupProcessStartTime;

  // 如果需要确保可见性，验证生成的数据
  if (ensureVisibility) {
    const hasVisibleData = validateMatrixDataVisibility(matrixData);
    if (!hasVisibleData) {
      process.env.NODE_ENV === 'development' && console.warn('生成的矩阵数据没有可见的数据点，这可能导致空白网格');
    }
  }

  // 更新缓存
  _cachedMatrixData = matrixData;
  _cacheTimestamp = now;

  const totalTime = performance.now() - startTime;
  process.env.NODE_ENV === 'development' && console.log(`✅ [MatrixUtils] 矩阵数据生成完成:`, {
    totalDataPoints,
    coordinateCount: matrixData.byCoordinate.size,
    groupProcessTime: `${groupProcessTime.toFixed(2)}ms`,
    totalTime: `${totalTime.toFixed(2)}ms`
  });

  // 性能警告
  if (totalTime > 50) {
    console.warn(`⚠️ [MatrixUtils] 数据生成耗时较长 (${totalTime.toFixed(2)}ms)，数据点: ${totalDataPoints}`);
  }

  return matrixData;
};

/**
 * 验证矩阵数据是否有可见的数据点
 * @param matrixData 矩阵数据
 * @returns 是否有可见数据点
 */
export const validateMatrixDataVisibility = (matrixData: MatrixData): boolean => {
  // 检查是否有任何坐标有数据点
  return matrixData.byCoordinate.size > 0;
};

/**
 * 生成网格数据 - 供矩阵系统视图渲染使用
 * 创建33x33网格，以中心为(0,0)的坐标系统，序号545为中心点
 * @returns 网格单元格数据数组
 */
export const generateGridData = (): CellData[] => {
  const gridData: CellData[] = [];
  const centerX = GRID_CENTER.X;
  const centerY = GRID_CENTER.Y;

  for (let row = 0; row < GRID_DIMENSIONS.ROWS; row++) {
    for (let col = 0; col < GRID_DIMENSIONS.COLS; col++) {
      // 使用以中心为(0,0)的坐标系统，序号545为中心点
      const x = col - centerX;
      const y = centerY - row;
      const cellId = row * GRID_DIMENSIONS.COLS + col;
      const number = cellId + 1;

      gridData.push({
        id: `cell-${cellId}`,
        row,
        col,
        x,
        y,
        index: cellId,
        color: 'transparent',    // 使用透明而不是灰色
        colorMappingValue: 0,    // 默认映射值
        level: 1,                // 默认级别
        group: null,             // 默认分组
        isActive: false,         // 默认未激活状态
        number,                  // 兼容字段
      });
    }
  }

  return gridData;
};
