/**
 * 优化的数据初始化管理器
 * 🎯 核心价值：解决数据加载时间较长的问题，提供高效的数据初始化
 * 📦 功能范围：智能缓存、性能监控、渐进式加载、错误恢复
 * ⚡ 性能优化：减少重复初始化、智能缓存失效、自适应性能调整
 */

import { IntelligentCacheManager } from '@/lib/cache/IntelligentCacheManager';
import { globalPerformanceDetector } from '@/lib/performance/DevicePerformanceDetector';
import { generateMatrixData, generateGridData } from '@/lib/utils/matrixUtils';
import { globalModeAwareGenerator } from '@/lib/utils/modeAwareDataGenerator';
import type { MatrixData, CellData } from '@/lib/types/matrix';
import {
  DataMode,
  DISPLAY_MODE_TO_DATA_MODE
} from '@/lib/types/grid-data-modes';
import type {
  GridDataSet,
  DataGenerationConfig
} from '@/lib/types/grid-data-modes';

export interface InitializationConfig {
  enableCache: boolean;
  enableProgressiveLoading: boolean;
  enablePerformanceOptimization: boolean;
  enableModeAwareGeneration: boolean; // 新增：模式感知生成
  maxInitializationTime: number; // ms
  retryAttempts: number;
  batchSize: number;
}

export interface InitializationProgress {
  phase: 'starting' | 'matrix-data' | 'grid-data' | 'validation' | 'caching' | 'complete' | 'error';
  progress: number; // 0-100
  message: string;
  estimatedTimeRemaining: number; // ms
  currentStep: string;
}

export interface InitializationResult {
  success: boolean;
  matrixData: MatrixData | null;
  gridData: CellData[] | null;
  modeAwareData?: GridDataSet; // 新增：模式感知数据
  dataMode?: DataMode; // 新增：使用的数据模式
  duration: number;
  cacheHit: boolean;
  performanceMetrics: {
    matrixDataTime: number;
    gridDataTime: number;
    modeAwareDataTime?: number; // 新增：模式感知数据生成时间
    validationTime: number;
    cachingTime: number;
  };
  errors: Error[];
}

export interface InitializationMetrics {
  totalInitializations: number;
  successfulInitializations: number;
  averageInitializationTime: number;
  cacheHitRate: number;
  errorRate: number;
  lastInitializationTime: number;
}

/**
 * 优化的数据初始化管理器类
 */
export class OptimizedDataInitializer {
  private cache: IntelligentCacheManager<any>;
  private config: InitializationConfig;
  private metrics: InitializationMetrics;
  private progressCallbacks = new Set<(progress: InitializationProgress) => void>();
  private isInitializing = false;
  private initializationPromise: Promise<InitializationResult> | null = null;

  constructor(config: Partial<InitializationConfig> = {}) {
    this.config = {
      enableCache: true,
      enableProgressiveLoading: true,
      enablePerformanceOptimization: true,
      enableModeAwareGeneration: true, // 默认启用模式感知生成
      maxInitializationTime: 5000, // 5秒
      retryAttempts: 3,
      batchSize: 100,
      ...config
    };

    this.cache = new IntelligentCacheManager({
      maxSize: 50,
      maxMemoryUsage: 100 * 1024 * 1024, // 100MB
      defaultTTL: 30 * 60 * 1000, // 30分钟
      enableLRU: true,
      enableMetrics: true
    });

    this.metrics = {
      totalInitializations: 0,
      successfulInitializations: 0,
      averageInitializationTime: 0,
      cacheHitRate: 0,
      errorRate: 0,
      lastInitializationTime: 0
    };
  }

  /**
   * 初始化数据（主要入口）
   */
  async initializeData(forceRegenerate: boolean = false, displayMode?: string): Promise<InitializationResult> {
    // 防止重复初始化
    if (this.isInitializing && this.initializationPromise) {
      return this.initializationPromise;
    }

    this.isInitializing = true;
    this.initializationPromise = this.performInitialization(forceRegenerate, displayMode);

    try {
      const result = await this.initializationPromise;
      this.updateMetrics(result);
      return result;
    } finally {
      this.isInitializing = false;
      this.initializationPromise = null;
    }
  }

  /**
   * 执行实际的初始化过程
   */
  private async performInitialization(forceRegenerate: boolean, displayMode?: string): Promise<InitializationResult> {
    const startTime = performance.now();
    const result: InitializationResult = {
      success: false,
      matrixData: null,
      gridData: null,
      duration: 0,
      cacheHit: false,
      performanceMetrics: {
        matrixDataTime: 0,
        gridDataTime: 0,
        validationTime: 0,
        cachingTime: 0
      },
      errors: []
    };

    try {
      // 阶段1：检查缓存
      this.notifyProgress({
        phase: 'starting',
        progress: 0,
        message: '开始数据初始化...',
        estimatedTimeRemaining: this.config.maxInitializationTime,
        currentStep: '检查缓存'
      });

      if (this.config.enableCache && !forceRegenerate) {
        const cachedResult = await this.tryLoadFromCache();
        if (cachedResult) {
          result.matrixData = cachedResult.matrixData;
          result.gridData = cachedResult.gridData;
          result.cacheHit = true;
          result.success = true;
          result.duration = performance.now() - startTime;
          
          this.notifyProgress({
            phase: 'complete',
            progress: 100,
            message: '从缓存加载完成',
            estimatedTimeRemaining: 0,
            currentStep: '完成'
          });

          return result;
        }
      }

      // 阶段2：生成矩阵数据
      this.notifyProgress({
        phase: 'matrix-data',
        progress: 20,
        message: '生成矩阵数据...',
        estimatedTimeRemaining: this.config.maxInitializationTime * 0.8,
        currentStep: '矩阵数据生成'
      });

      const matrixStartTime = performance.now();
      result.matrixData = await this.generateMatrixDataOptimized();
      result.performanceMetrics.matrixDataTime = performance.now() - matrixStartTime;

      // 阶段3：生成网格数据
      this.notifyProgress({
        phase: 'grid-data',
        progress: 60,
        message: '生成网格数据...',
        estimatedTimeRemaining: this.config.maxInitializationTime * 0.4,
        currentStep: '网格数据生成'
      });

      const gridStartTime = performance.now();
      result.gridData = await this.generateGridDataOptimized();
      result.performanceMetrics.gridDataTime = performance.now() - gridStartTime;

      // 阶段3.5：生成模式感知数据（如果启用）
      if (this.config.enableModeAwareGeneration && displayMode) {
        this.notifyProgress({
          phase: 'grid-data',
          progress: 70,
          message: '生成模式优化数据...',
          estimatedTimeRemaining: this.config.maxInitializationTime * 0.3,
          currentStep: '模式感知数据生成'
        });

        const modeAwareStartTime = performance.now();
        const dataMode = DISPLAY_MODE_TO_DATA_MODE[displayMode as keyof typeof DISPLAY_MODE_TO_DATA_MODE] || DataMode.COORDINATE_ONLY;

        const config: DataGenerationConfig = {
          mode: dataMode,
          enableCache: this.config.enableCache,
          enableSpecialCharacters: true,
          gridSize: 33
        };

        result.modeAwareData = globalModeAwareGenerator.generateData(config);
        result.dataMode = dataMode;
        result.performanceMetrics.modeAwareDataTime = performance.now() - modeAwareStartTime;

        process.env.NODE_ENV === 'development' && console.log(`🎯 [OptimizedDataInitializer] 模式感知数据生成完成:`, {
          displayMode,
          dataMode,
          generationTime: `${result.performanceMetrics.modeAwareDataTime?.toFixed(2)}ms`,
          cellCount: result.modeAwareData.totalCells
        });
      }

      // 阶段4：数据验证
      this.notifyProgress({
        phase: 'validation',
        progress: 80,
        message: '验证数据完整性...',
        estimatedTimeRemaining: this.config.maxInitializationTime * 0.2,
        currentStep: '数据验证'
      });

      const validationStartTime = performance.now();
      await this.validateData(result.matrixData, result.gridData);
      result.performanceMetrics.validationTime = performance.now() - validationStartTime;

      // 阶段5：缓存数据
      if (this.config.enableCache) {
        this.notifyProgress({
          phase: 'caching',
          progress: 90,
          message: '缓存数据...',
          estimatedTimeRemaining: this.config.maxInitializationTime * 0.1,
          currentStep: '数据缓存'
        });

        const cachingStartTime = performance.now();
        await this.cacheData(result.matrixData, result.gridData);
        result.performanceMetrics.cachingTime = performance.now() - cachingStartTime;
      }

      result.success = true;
      result.duration = performance.now() - startTime;

      this.notifyProgress({
        phase: 'complete',
        progress: 100,
        message: `数据初始化完成 (${result.duration.toFixed(2)}ms)`,
        estimatedTimeRemaining: 0,
        currentStep: '完成'
      });

      // 性能警告
      if (result.duration > this.config.maxInitializationTime) {
        console.warn(`⚠️ [DataInitializer] 初始化时间超过预期 (${result.duration.toFixed(2)}ms > ${this.config.maxInitializationTime}ms)`);
      }

    } catch (error) {
      result.errors.push(error as Error);
      result.duration = performance.now() - startTime;
      
      this.notifyProgress({
        phase: 'error',
        progress: 0,
        message: `初始化失败: ${(error as Error).message}`,
        estimatedTimeRemaining: 0,
        currentStep: '错误'
      });

      console.error('🚨 [DataInitializer] 数据初始化失败:', error);
    }

    return result;
  }

  /**
   * 尝试从缓存加载数据
   */
  private async tryLoadFromCache(): Promise<{ matrixData: MatrixData; gridData: CellData[] } | null> {
    try {
      const matrixData = this.cache.get('matrix-data');
      const gridData = this.cache.get('grid-data');

      if (matrixData && gridData) {
        console.log('🚀 [DataInitializer] 从缓存加载数据成功');
        return { matrixData, gridData };
      }
    } catch (error) {
      console.warn('⚠️ [DataInitializer] 缓存加载失败:', error);
    }

    return null;
  }

  /**
   * 优化的矩阵数据生成
   */
  private async generateMatrixDataOptimized(): Promise<MatrixData> {
    if (this.config.enablePerformanceOptimization) {
      // 根据设备性能调整生成策略
      const capabilities = await globalPerformanceDetector.detectDeviceCapabilities();
      const isLowPerformance = capabilities.cpu.level === 'low' || capabilities.memory.level === 'low';

      if (isLowPerformance) {
        // 低性能设备使用简化生成
        return generateMatrixData(false, false);
      }
    }

    return generateMatrixData(true, false);
  }

  /**
   * 优化的网格数据生成
   */
  private async generateGridDataOptimized(): Promise<CellData[]> {
    if (this.config.enableProgressiveLoading) {
      // 渐进式生成网格数据
      return new Promise((resolve) => {
        // 使用 setTimeout 避免阻塞主线程
        setTimeout(() => {
          resolve(generateGridData());
        }, 0);
      });
    }

    return generateGridData();
  }

  /**
   * 验证数据完整性
   */
  private async validateData(matrixData: MatrixData | null, gridData: CellData[] | null): Promise<void> {
    if (!matrixData || !gridData) {
      throw new Error('数据生成失败：矩阵数据或网格数据为空');
    }

    if (matrixData.byCoordinate.size === 0) {
      throw new Error('矩阵数据无效：坐标映射为空');
    }

    if (gridData.length !== 1089) { // 33x33
      throw new Error(`网格数据无效：期望1089个单元格，实际${gridData.length}个`);
    }
  }

  /**
   * 缓存数据
   */
  private async cacheData(matrixData: MatrixData | null, gridData: CellData[] | null): Promise<void> {
    if (!matrixData || !gridData) return;

    this.cache.set('matrix-data', matrixData, {
      tags: ['matrix', 'core-data'],
      dependencies: ['color-config', 'group-config']
    });

    this.cache.set('grid-data', gridData, {
      tags: ['grid', 'core-data'],
      dependencies: ['grid-config']
    });
  }

  /**
   * 通知进度更新
   */
  private notifyProgress(progress: InitializationProgress): void {
    this.progressCallbacks.forEach(callback => {
      try {
        callback(progress);
      } catch (error) {
        console.error('进度回调执行失败:', error);
      }
    });
  }

  /**
   * 更新性能指标
   */
  private updateMetrics(result: InitializationResult): void {
    this.metrics.totalInitializations++;
    
    if (result.success) {
      this.metrics.successfulInitializations++;
    }

    // 更新平均初始化时间
    const totalTime = this.metrics.averageInitializationTime * (this.metrics.totalInitializations - 1) + result.duration;
    this.metrics.averageInitializationTime = totalTime / this.metrics.totalInitializations;

    // 更新缓存命中率
    const cacheHits = result.cacheHit ? 1 : 0;
    this.metrics.cacheHitRate = (this.metrics.cacheHitRate * (this.metrics.totalInitializations - 1) + cacheHits) / this.metrics.totalInitializations;

    // 更新错误率
    this.metrics.errorRate = (this.metrics.totalInitializations - this.metrics.successfulInitializations) / this.metrics.totalInitializations;

    this.metrics.lastInitializationTime = result.duration;
  }

  /**
   * 订阅进度更新
   */
  onProgress(callback: (progress: InitializationProgress) => void): () => void {
    this.progressCallbacks.add(callback);
    return () => this.progressCallbacks.delete(callback);
  }

  /**
   * 清除缓存
   */
  clearCache(): void {
    this.cache.clear();
  }

  /**
   * 获取性能指标
   */
  getMetrics(): InitializationMetrics {
    return { ...this.metrics };
  }

  /**
   * 获取缓存统计
   */
  getCacheStats() {
    return this.cache.getMetrics();
  }

  /**
   * 销毁初始化器
   */
  destroy(): void {
    this.cache.destroy();
    this.progressCallbacks.clear();
  }
}

// 创建全局数据初始化器实例
export const globalDataInitializer = new OptimizedDataInitializer({
  enableCache: true,
  enableProgressiveLoading: true,
  enablePerformanceOptimization: true,
  enableModeAwareGeneration: true, // 启用模式感知生成
  maxInitializationTime: 3000, // 3秒
  retryAttempts: 2,
  batchSize: 100
});

// 数据初始化器工厂函数
export function createDataInitializer(config?: Partial<InitializationConfig>): OptimizedDataInitializer {
  return new OptimizedDataInitializer(config);
}
