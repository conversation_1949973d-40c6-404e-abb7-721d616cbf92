/**
 * 网格数据模式类型定义
 * 🎯 核心价值：为不同显示模式提供优化的数据结构，减少不必要的数据加载
 * 📦 功能范围：坐标模式、数值模式、颜色模式的专用数据类型
 * ⚡ 性能优化：按需加载数据，减少内存使用和处理时间
 */

import type { BasicColorType, GroupType } from '@/lib/types/matrix';

// 数据模式枚举
export enum DataMode {
  COORDINATE_ONLY = 'coordinate-only',    // 仅坐标数据
  BASIC_RENDER = 'basic-render',          // 基础渲染数据
  FULL_RENDER = 'full-render'             // 完整渲染数据
}

// 基础坐标接口
export interface BaseCoordinate {
  x: number;
  y: number;
  id: string;
  row: number;
  col: number;
  index: number;
}

// 特殊字符信息
export interface SpecialCharacterInfo {
  character: string;
  isSpecial: boolean;
}

// 轻量级坐标数据（仅用于坐标模式）
export interface CoordinateOnlyData extends BaseCoordinate {
  // 特殊字符信息（黑色格子的字母）
  specialChar?: SpecialCharacterInfo;
  // 最小化的激活状态
  isActive: boolean;
}

// 基础渲染数据（用于数值模式）
export interface BasicRenderData extends BaseCoordinate {
  // 颜色映射值
  colorMappingValue: number;
  // 等级信息
  level: number;
  // 基础颜色
  color: string;
  // 特殊字符信息
  specialChar?: SpecialCharacterInfo;
  // 激活状态
  isActive: boolean;
}

// 完整渲染数据（用于颜色模式）
export interface FullRenderData extends BaseCoordinate {
  // 完整的颜色信息
  color: string;
  colorMappingValue: number;
  colorType?: BasicColorType;
  // 等级和组信息
  level: number;
  group: GroupType | null;
  // 特殊字符信息
  specialChar?: SpecialCharacterInfo;
  // 完整的状态信息
  isActive: boolean;
  // 向后兼容字段
  number: number;
}

// 联合类型
export type GridDataByMode = CoordinateOnlyData | BasicRenderData | FullRenderData;

// 数据集合接口
export interface CoordinateOnlyDataSet {
  mode: DataMode.COORDINATE_ONLY;
  cells: CoordinateOnlyData[][];
  specialCharacters: Map<string, SpecialCharacterInfo>; // key: "x,y"
  totalCells: number;
}

export interface BasicRenderDataSet {
  mode: DataMode.BASIC_RENDER;
  cells: BasicRenderData[][];
  coordinateMap: Map<string, { colorMappingValue: number; level: number }>; // key: "x,y"
  specialCharacters: Map<string, SpecialCharacterInfo>;
  totalCells: number;
}

export interface FullRenderDataSet {
  mode: DataMode.FULL_RENDER;
  cells: FullRenderData[][];
  coordinateMap: Map<string, { color: BasicColorType; level: number; group: GroupType | null }>;
  specialCharacters: Map<string, SpecialCharacterInfo>;
  matrixData?: any; // 完整的矩阵数据（可选）
  totalCells: number;
}

// 联合数据集类型
export type GridDataSet = CoordinateOnlyDataSet | BasicRenderDataSet | FullRenderDataSet;

// 数据生成配置
export interface DataGenerationConfig {
  mode: DataMode;
  enableCache: boolean;
  enableSpecialCharacters: boolean;
  gridSize: number;
}

// 性能指标
export interface DataModeMetrics {
  mode: DataMode;
  generationTime: number;
  memoryUsage: number;
  cellCount: number;
  cacheHit: boolean;
}

// 模式切换配置
export interface ModeSwitchConfig {
  fromMode: DataMode;
  toMode: DataMode;
  preserveCache: boolean;
  enableTransition: boolean;
}

// 数据模式工具函数类型
export interface DataModeUtils {
  getRequiredDataMode(displayMode: string): DataMode;
  canUpgradeData(from: DataMode, to: DataMode): boolean;
  estimateMemoryUsage(mode: DataMode, cellCount: number): number;
  getOptimalMode(deviceCapabilities: any): DataMode;
}

// 缓存键生成器
export interface CacheKeyGenerator {
  generateKey(mode: DataMode, config: DataGenerationConfig): string;
  generateModeKey(mode: DataMode): string;
  generateConfigKey(config: Partial<DataGenerationConfig>): string;
}

// 数据验证器
export interface DataValidator {
  validateCoordinateData(data: CoordinateOnlyData[][]): boolean;
  validateBasicRenderData(data: BasicRenderData[][]): boolean;
  validateFullRenderData(data: FullRenderData[][]): boolean;
  validateDataSet(dataSet: GridDataSet): boolean;
}

// 数据转换器
export interface DataConverter {
  upgradeToBasicRender(coordinateData: CoordinateOnlyDataSet): BasicRenderDataSet;
  upgradeToFullRender(basicData: BasicRenderDataSet): FullRenderDataSet;
  downgradeToBasicRender(fullData: FullRenderDataSet): BasicRenderDataSet;
  downgradeToCoordinateOnly(data: BasicRenderDataSet | FullRenderDataSet): CoordinateOnlyDataSet;
}

// 性能监控器
export interface PerformanceMonitor {
  startMeasurement(mode: DataMode): string;
  endMeasurement(measurementId: string): DataModeMetrics;
  getAverageMetrics(mode: DataMode): DataModeMetrics | null;
  compareModesPerformance(): Record<DataMode, DataModeMetrics>;
}

// 导出常量
export const DATA_MODE_MEMORY_ESTIMATES = {
  [DataMode.COORDINATE_ONLY]: 0.3, // 相对内存使用（基准为1）
  [DataMode.BASIC_RENDER]: 0.6,
  [DataMode.FULL_RENDER]: 1.0
} as const;

export const DATA_MODE_GENERATION_COMPLEXITY = {
  [DataMode.COORDINATE_ONLY]: 1, // 相对复杂度
  [DataMode.BASIC_RENDER]: 3,
  [DataMode.FULL_RENDER]: 5
} as const;

// 显示模式到数据模式的映射
export const DISPLAY_MODE_TO_DATA_MODE = {
  'coordinates': DataMode.COORDINATE_ONLY,
  'value': DataMode.BASIC_RENDER,
  'color': DataMode.FULL_RENDER
} as const;
