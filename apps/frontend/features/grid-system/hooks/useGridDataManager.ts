/**
 * useGridDataManager Hook - 业务逻辑版本
 * 
 * 网格数据管理的业务逻辑hook，从原components/grid-system/hooks/useGridData.ts迁移
 * 🎯 核心价值：集中管理网格数据的业务逻辑，与UI层分离
 * 📦 功能范围：数据获取、状态管理、业务规则、数据验证
 */

import { useMemo, useCallback, useState, useEffect } from 'react';
import { useBasicDataStore } from '@/stores/basicDataStore';
import { logger } from '@/lib/hooks/useLogManager';
import type { BasicColorType, GroupType } from '@/lib/types/matrix';
import { ColorMappingService } from '@/lib/services/ColorMappingService';
import { SpecialCoordinateService } from '@/lib/services/SpecialCoordinateService';

import type { CellData } from '@/lib/types/grid';
import type { UseGridDataManagerReturn } from '../types';

export const useGridDataManager = (): UseGridDataManagerReturn => {
  const [error, setError] = useState<Error | null>(null);
  const [isHydrated, setIsHydrated] = useState(false);
  const [isDirty, setIsDirty] = useState(false);
  const [isLoading, setIsLoading] = useState(false);

  // Get data from basicDataStore - 使用新的矩阵数据结构
  const matrixData = useBasicDataStore((state) => state.matrixData);
  const colorVisibility = useBasicDataStore((state) => state.colorVisibility);
  const colorValues = useBasicDataStore((state) => state.colorValues);
  const blackCellData = useBasicDataStore((state) => state.blackCellData);
  const groupVisibility = useBasicDataStore((state) => state.groupVisibility);
  const regenerateMatrixData = useBasicDataStore((state) => state.regenerateMatrixData);
  const initializeMatrixData = useBasicDataStore((state) => state.initializeMatrixData);

  // 注释掉重复的初始化逻辑 - 避免与 useGridData 中的初始化冲突
  // 现在由 useGridData hook 统一管理数据初始化
  useEffect(() => {
    logger.debug('业务数据管理器初始化开始', undefined, 'useGridData');
    setIsHydrated(true);
    // initializeMatrixData(); // 移除重复调用，避免性能问题
  }, []); // 移除 initializeMatrixData 依赖，避免重复执行

  // Create coordinate lookup map for efficient cell activation checking
  const coordinateMap = useMemo(() => {
    const map = new Map<string, { color: BasicColorType; level: number; group: GroupType | null }>();

    if (!isHydrated || !matrixData) {
      return map;
    }

    // 使用新的matrixData结构生成坐标映射
    if (matrixData.byCoordinate) {
      Object.entries(matrixData.byCoordinate).forEach(([coordKey, dataPoint]) => {
        if (dataPoint && dataPoint.color && dataPoint.level) {
          map.set(coordKey, {
            color: dataPoint.color,
            level: dataPoint.level,
            group: dataPoint.group || null
          });
        }
      });
    }

    logger.debug(`坐标映射创建完成，包含 ${map.size} 个数据点`, undefined, 'useGridData');
    return map;
  }, [matrixData, isHydrated]);

  // 检查单元格是否激活的业务逻辑
  const isCellActive = useCallback((x: number, y: number): boolean => {
    if (!isHydrated) return false;

    const coordKey = `${x},${y}`;
    const dataPoint = coordinateMap.get(coordKey);
    
    if (!dataPoint) return false;

    // 检查颜色可见性
    const isColorVisible = colorVisibility[dataPoint.color] ?? true;
    
    // 检查组可见性（如果有组）
    const isGroupVisible = dataPoint.group ? (groupVisibility[dataPoint.group] ?? true) : true;
    
    return isColorVisible && isGroupVisible;
  }, [coordinateMap, colorVisibility, groupVisibility, isHydrated]);



  // 生成网格数据的业务逻辑
  const gridData = useMemo((): CellData[][] => {
    if (!isHydrated) {
      return Array(33).fill(null).map(() => Array(33).fill(null));
    }

    try {
      const grid: CellData[][] = [];
      const gridSize = 33;
      const center = Math.floor(gridSize / 2);

      for (let row = 0; row < gridSize; row++) {
        const gridRow: CellData[] = [];
        
        for (let col = 0; col < gridSize; col++) {
          // 转换为以中心为原点的坐标系
          const x = col - center;
          const y = center - row;
          
          const cellData: CellData = {
            id: `cell-${x}-${y}`,
            row,
            col,
            x,
            y,
            index: row * gridSize + col,
            color: 'transparent', // 默认透明，具体颜色由渲染引擎计算
            colorMappingValue: 0, // 将在后续计算中设置
            level: 1, // 默认级别
            group: null,
            isActive: isCellActive(x, y),
            number: row * gridSize + col, // 向后兼容
          };

          // 设置颜色映射值和级别
          const coordKey = `${x},${y}`;
          const dataPoint = coordinateMap.get(coordKey);
          if (dataPoint) {
            cellData.colorMappingValue = ColorMappingService.getNumericValue(dataPoint.color, dataPoint.level);
            cellData.level = dataPoint.level;
            cellData.group = dataPoint.group; // 直接使用字母组，不需要转换
          } else {
            // 检查是否是特殊坐标
            const specialChar = SpecialCoordinateService.getCharacterForCoordinate(x, y);
            if (specialChar) {
              cellData.colorMappingValue = SpecialCoordinateService.getNumericValueForCharacter(specialChar);
              cellData.group = null;
            }
          }

          gridRow.push(cellData);
        }
        
        grid.push(gridRow);
      }

      return grid;
    } catch (error) {
      setError(error as Error);
      return Array(33).fill(null).map(() => Array(33).fill(null));
    }
  }, [coordinateMap, colorValues, colorVisibility, groupVisibility, isHydrated, isCellActive]);

  // 刷新数据
  const refreshData = useCallback(async (): Promise<void> => {
    setIsLoading(true);
    setError(null);
    
    try {
      await regenerateMatrixData();
    } catch (error) {
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [regenerateMatrixData]);

  // 保存数据（业务逻辑）
  const saveData = useCallback(async (): Promise<void> => {
    if (!isDirty) return;
    
    setIsLoading(true);
    setError(null);
    
    try {
      // 这里可以添加保存到服务器的逻辑
      // await saveGridDataToServer(gridData);
      setIsDirty(false);
    } catch (error) {
      setError(error as Error);
    } finally {
      setIsLoading(false);
    }
  }, [isDirty]);

  return {
    gridData,
    refreshData,
    saveData,
    isDirty,
    isLoading,
    error,
  };
};
