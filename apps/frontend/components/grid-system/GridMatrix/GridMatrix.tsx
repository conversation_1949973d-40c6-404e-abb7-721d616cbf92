/**
 * GridMatrix Component - 纯UI版本
 * 
 * 纯UI网格矩阵组件，专注于渲染和视觉呈现
 * 🎯 核心价值：提供高性能的网格UI渲染，不包含业务逻辑
 * 📦 功能范围：UI渲染、事件传递、样式控制、可访问性
 * 🔄 重构说明：从原GridMatrix.tsx中移除业务逻辑，保留纯UI功能
 */

'use client';

import React, { memo, useCallback, useMemo, useState, useRef, useEffect, KeyboardEvent } from 'react';
import { cn } from '@/lib/utils/cn';
import { GridCell } from '../GridCell';
import { GridErrorBoundary } from '../GridErrorBoundary';
import { GridLoadingState } from '../GridLoadingState';
import { useGridData } from '../hooks/useGridData';
import { useGridAnimation } from '../hooks/useGridAnimation';
import { useGridRenderingEngine } from '@/lib/rendering';
import type { GridMatrixProps, UIGridConfig } from './types';
import { DEFAULT_MATRIX_UI_CONFIG } from './types';
import type { CellData } from '@/lib/types/grid';
import styles from './GridMatrix.module.css';

/**
 * GridMatrix - 纯UI网格矩阵组件
 * 专注于网格的视觉渲染和用户交互
 */
const GridMatrixContent = memo<GridMatrixProps>(({
  cells: externalCells,
  config: userConfig,
  className,
  style,
  onCellClick,
  onCellHover,
  onCellFocus,
  onCellDoubleClick,
  getCellContent,
  getCellStyle,
  getCellClassName,
  ariaLabel = '网格矩阵',
  ariaDescription,
  enableVirtualization = false, // TODO: 实现虚拟化
  renderBatchSize = 100, // TODO: 实现批量渲染
}) => {
  // 合并配置 - 使用统一的默认配置
  const config: UIGridConfig = useMemo(() => ({
    ...DEFAULT_MATRIX_UI_CONFIG,
    ...userConfig,
  }), [userConfig]);

  // 获取网格数据（如果没有外部提供）
  const {
    cells: internalCells,
    isLoading,
    error,
    isCellActive,
  } = useGridData();

  // 使用统一渲染引擎
  const {
    getCellRenderData,
    isReady: renderingEngineReady,
  } = useGridRenderingEngine({
    enableCache: true,
    batchSize: renderBatchSize,
    autoUpdateConfig: true,
  });

  // 使用外部数据或内部数据
  const cells = externalCells || internalCells;

  // 数据准备状态检查 - 移到cells定义之后
  const isDataReady = useMemo(() => {
    return cells && cells.length > 0 && !isLoading && renderingEngineReady;
  }, [cells, isLoading, renderingEngineReady]);

  // 添加加载超时机制，但只在开发环境下警告，不强制渲染
  const [loadingTimeout, setLoadingTimeout] = useState(false);
  useEffect(() => {
    if (isLoading) {
      const timer = setTimeout(() => {
        if (process.env.NODE_ENV === 'development') {
          console.warn('🔍 [GridMatrix] 数据加载时间较长，请检查数据初始化逻辑');
        }
        setLoadingTimeout(true);
      }, 5000); // 5秒超时

      return () => clearTimeout(timer);
    } else {
      setLoadingTimeout(false);
    }
  }, [isLoading]);

  // 动画hook
  const {
    animationClass,
    isAnimating,
    animationStyle,
    getScaleStyle: _getScaleStyle, // 暂未使用，保留用于未来扩展
    cssVariables,
  } = useGridAnimation(config);

  // 状态管理
  const [hoveredCell, setHoveredCell] = useState<CellData | null>(null);
  const [focusedCell, setFocusedCell] = useState<CellData | null>(null);
  const gridRef = useRef<HTMLDivElement>(null);

  // 处理单元格点击
  const handleCellClick = useCallback((cell: CellData, event: React.MouseEvent) => {
    onCellClick?.(cell, event);
  }, [onCellClick]);

  // 处理单元格双击
  const handleCellDoubleClick = useCallback((cell: CellData, event: React.MouseEvent) => {
    onCellDoubleClick?.(cell, event);
  }, [onCellDoubleClick]);

  // 处理单元格悬停
  const handleCellHover = useCallback((cell: CellData | null) => {
    setHoveredCell(cell);
    onCellHover?.(cell);
  }, [onCellHover]);

  // 处理单元格焦点
  const handleCellFocus = useCallback((cell: CellData) => {
    setFocusedCell(cell);
    onCellFocus?.(cell);
  }, [onCellFocus]);

  // 统一的单元格渲染数据获取（使用渲染引擎）
  const getCellRenderDataInternal = useCallback((cell: CellData) => {
    // 使用统一渲染引擎获取基础渲染数据
    const baseRenderData = getCellRenderData(cell);

    // 合并外部提供的自定义逻辑
    const finalRenderData = {
      ...baseRenderData,
      // 优先使用外部提供的内容获取函数
      content: getCellContent ? getCellContent(cell, config.displayMode) : baseRenderData.content,
      // 合并外部样式
      style: getCellStyle ? { ...baseRenderData.style, ...getCellStyle(cell) } : baseRenderData.style,
      // 合并外部类名
      className: getCellClassName ? cn(baseRenderData.className, getCellClassName(cell)) : baseRenderData.className,
    };

    // 添加状态相关的类名
    const stateClasses = [
      styles.cell,
      config.cellShape === 'rounded' ? styles.cellRounded : styles.cellSquare,
    ];

    if (cell === hoveredCell) stateClasses.push(styles.cellHovered);
    if (cell === focusedCell) stateClasses.push(styles.cellFocused);
    if (isCellActive(cell.x, cell.y)) stateClasses.push(styles.cellActive);

    finalRenderData.className = cn(...stateClasses, finalRenderData.className);

    // 确保样式包含配置的尺寸和字体
    finalRenderData.style = {
      ...finalRenderData.style,
      width: `${config.size}px`,
      height: `${config.size}px`,
      fontSize: `${config.fontSize}px`,
    };

    return finalRenderData;
  }, [
    getCellRenderData,
    getCellContent,
    getCellStyle,
    getCellClassName,
    config.displayMode,
    config.cellShape,
    config.size,
    config.fontSize,
    hoveredCell,
    focusedCell,
    isCellActive,
  ]);

  // 键盘导航处理
  const handleKeyDown = useCallback((event: KeyboardEvent<HTMLDivElement>) => {
    if (!focusedCell) return;

    let newFocusedCell: CellData | null = null;

    switch (event.key) {
      case 'ArrowUp':
        newFocusedCell = cells[focusedCell.row - 1]?.[focusedCell.col];
        break;
      case 'ArrowDown':
        newFocusedCell = cells[focusedCell.row + 1]?.[focusedCell.col];
        break;
      case 'ArrowLeft':
        newFocusedCell = cells[focusedCell.row]?.[focusedCell.col - 1];
        break;
      case 'ArrowRight':
        newFocusedCell = cells[focusedCell.row]?.[focusedCell.col + 1];
        break;
      case 'Enter':
      case ' ':
        // 触发点击事件
        if (focusedCell) {
          handleCellClick(focusedCell, event as any);
        }
        break;
    }

    if (newFocusedCell) {
      handleCellFocus(newFocusedCell);
      event.preventDefault();
    }
  }, [focusedCell, cells, handleCellClick, handleCellFocus]);

  // 网格样式
  const gridStyle = useMemo((): React.CSSProperties => ({
    ...style,
    ...animationStyle,
    ...cssVariables,
    display: 'grid',
    gridTemplateColumns: `repeat(33, ${config.size}px)`,
    gridTemplateRows: `repeat(33, ${config.size}px)`,
    gap: `${config.gap}px`,
    padding: `${config.padding}px`,
  }), [style, animationStyle, cssVariables, config]);

  // 渲染加载状态 - 确保数据完全准备好才渲染矩阵
  if (!isDataReady) {
    return (
      <GridLoadingState
        isLoading={true}
        loadingMessage={loadingTimeout ? "数据加载中，请稍候..." : "加载网格数据中..."}
        className={className}
      />
    );
  }

  // 渲染错误状态
  if (error) {
    return (
      <GridLoadingState
        isLoading={false}
        error={error}
        errorMessage="网格数据加载失败"
        onRetry={() => window.location.reload()}
        className={className}
      />
    );
  }

  // 渲染空状态
  if (!cells || cells.length === 0) {
    return (
      <GridLoadingState
        isLoading={false}
        isEmpty={true}
        emptyMessage="暂无网格数据"
        className={className}
      />
    );
  }

  return (
    <div
      ref={gridRef}
      className={cn(
        styles.gridMatrix,
        animationClass,
        isAnimating && styles.animating,
        className
      )}
      style={gridStyle}
      onKeyDown={handleKeyDown}
      tabIndex={0}
      role="grid"
      aria-label={ariaLabel}
      aria-description={ariaDescription}
      aria-rowcount={cells.length}
      aria-colcount={cells[0]?.length || 0}
    >
      {cells.map((row, rowIndex) =>
        row.map((cell, colIndex) => {
          // 处理hydration期间的null值
          if (!cell) {
            return (
              <div
                key={`placeholder-${rowIndex}-${colIndex}`}
                style={{
                  width: `${config.size}px`,
                  height: `${config.size}px`,
                  backgroundColor: 'transparent', // 使用透明而不是灰色
                }}
              />
            );
          }

          // 获取统一的渲染数据
          const renderData = getCellRenderDataInternal(cell);

          return (
            <GridCell
              key={`${cell.x}-${cell.y}`}
              cell={cell}
              config={config}
              isActive={renderData.isActive}
              onClick={handleCellClick}
              onDoubleClick={handleCellDoubleClick}
              onHover={handleCellHover}
              onFocus={handleCellFocus}
              isFocused={cell === focusedCell}
              id={`grid-cell-${cell.x}-${cell.y}`}
              getCellContent={() => renderData.content}
              getCellStyle={() => renderData.style}
              getCellClassName={() => renderData.className}
            />
          );
        })
      )}
    </div>
  );
});

GridMatrixContent.displayName = 'GridMatrixContent';

/**
 * GridMatrix with Error Boundary
 */
export const GridMatrix = memo<GridMatrixProps>((props) => (
  <GridErrorBoundary>
    <GridMatrixContent {...props} />
  </GridErrorBoundary>
));

GridMatrix.displayName = 'GridMatrix';
