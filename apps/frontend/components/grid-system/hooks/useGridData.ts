/**
 * useGridData Hook - 纯UI版本
 * 
 * 为纯UI组件提供网格数据的简化hook
 * 🎯 核心价值：提供UI渲染所需的最小数据集，不包含复杂业务逻辑
 * 📦 功能范围：数据获取、基础状态、简单计算
 * 🔄 重构说明：从原useGridData中提取UI必需功能，业务逻辑移至features
 */

import { useMemo, useCallback, useState, useEffect } from 'react';
import { useBasicDataStore } from '@/stores/basicDataStore';
import { useGridConfigActions, useGridConfigStore } from '@/stores/gridConfigStore';
import type { BasicColorType, GroupType } from '@/lib/types/matrix';
import { ColorMappingService } from '@/lib/services/ColorMappingService';
import { SpecialCoordinateService } from '@/lib/services/SpecialCoordinateService';
import type { CellData } from '@/lib/types/grid';

export interface UseGridDataReturn {
  cells: CellData[][];
  isLoading: boolean;
  error: Error | null;
  refreshData: () => void;
  // UI辅助函数
  isCellActive: (x: number, y: number) => boolean;
  getCellContent: (cell: CellData, displayMode: string) => string | null;
}

export interface UseGridDataOptions {
  // 保留接口以便向后兼容，但不再使用 colorModeEnabled
}

export const useGridData = (): UseGridDataReturn => {
  const [error, setError] = useState<Error | null>(null);
  const [isHydrated, setIsHydrated] = useState(false);

  // 使用 store 中的方法
  const { isCellActive: storeIsCellActive } = useGridConfigActions();

  // 获取基础数据
  const matrixData = useBasicDataStore((state) => state.matrixData);
  const _isHydrated = useBasicDataStore((state) => state._isHydrated);
  const colorVisibility = useBasicDataStore((state) => state.colorVisibility);
  const colorValues = useBasicDataStore((state) => state.colorValues);
  const groupVisibility = useBasicDataStore((state) => state.groupVisibility);
  const regenerateMatrixData = useBasicDataStore((state) => state.regenerateMatrixData);
  const initializeMatrixData = useBasicDataStore((state) => state.initializeMatrixData);

  // 获取当前显示模式
  const baseDisplayMode = useGridConfigStore((state) => state.baseDisplayMode);

  // 稳定化初始化函数引用，避免不必要的重新执行
  const stableInitializeMatrixData = useCallback(() => {
    initializeMatrixData();
  }, [initializeMatrixData]);

  // 优化的数据初始化和hydration状态管理
  useEffect(() => {
    const startTime = performance.now();

    // 确保数据完整性：只有当数据真正存在且有效时才设置hydrated
    if (_isHydrated && matrixData && matrixData.byCoordinate.size > 0) {
      setIsHydrated(true);
      process.env.NODE_ENV === 'development' && console.log(`🔍 [GridData] 数据已就绪，耗时: ${(performance.now() - startTime).toFixed(2)}ms`);
      return;
    }

    // 如果没有数据，进行初始化（但避免重复调用）
    if (!matrixData || matrixData.byCoordinate.size === 0) {
      process.env.NODE_ENV === 'development' && console.log('🔍 [GridData] 开始初始化矩阵数据');

      // 使用稳定的函数引用
      stableInitializeMatrixData();

      // 设置hydrated状态，让组件知道初始化已开始
      setIsHydrated(true);

      process.env.NODE_ENV === 'development' && console.log(`🔍 [GridData] 初始化完成，耗时: ${(performance.now() - startTime).toFixed(2)}ms`);
      return;
    }

    // 数据存在时设置hydrated状态
    setIsHydrated(true);
    process.env.NODE_ENV === 'development' && console.log(`🔍 [GridData] 状态更新，耗时: ${(performance.now() - startTime).toFixed(2)}ms`);
  }, [_isHydrated, matrixData, stableInitializeMatrixData]); // 使用稳定的函数引用

  // 优化的坐标映射 - 添加性能监控和缓存
  const coordinateMap = useMemo(() => {
    const startTime = performance.now();
    const map = new Map<string, { color: BasicColorType; level: number; group: GroupType | null }>();

    if (!isHydrated || !matrixData?.byCoordinate) {
      process.env.NODE_ENV === 'development' && console.log(`🔍 [GridData] coordinateMap跳过生成（数据未就绪），耗时: ${(performance.now() - startTime).toFixed(2)}ms`);
      return map;
    }

    // 批量处理坐标映射以提高性能
    let processedCount = 0;
    matrixData.byCoordinate.forEach((dataPoints, coordKey) => {
      if (Array.isArray(dataPoints) && dataPoints.length > 0) {
        const firstDataPoint = dataPoints[0]; // 取第一个数据点
        if (firstDataPoint && firstDataPoint.color && firstDataPoint.level) {
          map.set(coordKey, {
            color: firstDataPoint.color,
            level: firstDataPoint.level,
            group: firstDataPoint.group || null
          });
          processedCount++;
        }
      }
    });

    const processingTime = performance.now() - startTime;
    process.env.NODE_ENV === 'development' && console.log(`🔍 [GridData] coordinateMap生成完成:`, {
      dataPoints: map.size,
      processedCount,
      processingTime: `${processingTime.toFixed(2)}ms`
    });

    // 性能警告
    if (processingTime > 20) {
      console.warn(`⚠️ [GridData] coordinateMap生成耗时较长 (${processingTime.toFixed(2)}ms)，数据点: ${map.size}`);
    }

    return map;
  }, [matrixData, isHydrated]);

  // 检查单元格是否激活 - 使用 store 中的逻辑，但考虑可见性过滤器
  const isCellActive = useCallback((x: number, y: number): boolean => {
    if (!isHydrated) return false;

    // 检查是否为黑色特殊坐标，黑色格子始终激活
    const specialChar = SpecialCoordinateService.getCharacterForCoordinate(x, y);
    if (specialChar) {
      return true;
    }

    // 首先检查 store 中的基础激活逻辑
    const baseActive = storeIsCellActive(x, y);
    if (!baseActive) return false;

    // 在颜色模式下，所有坐标都应该是激活的，不需要检查coordinateMap
    if (baseDisplayMode === 'color') {
      return true;
    }

    // 在其他模式下，应用可见性过滤器
    const coordKey = `${x},${y}`;
    const dataPoint = coordinateMap.get(coordKey);

    if (!dataPoint) return false;

    const isColorVisible = colorVisibility[dataPoint.color] ?? true;
    const isGroupVisible = dataPoint.group ? (groupVisibility[dataPoint.group] ?? true) : true;

    return isColorVisible && isGroupVisible;
  }, [coordinateMap, colorVisibility, groupVisibility, isHydrated, storeIsCellActive, baseDisplayMode]);



  // 获取单元格内容
  // @deprecated 此方法已被GridRenderingEngine.calculateCellContent替代
  // 保留用于向后兼容，建议使用useGridRenderingEngine
  const getCellContent = useCallback((cell: CellData, displayMode: string): string | null => {
    if (!isHydrated || !cell) return null;

    try {
      // 首先检查是否为黑色特殊坐标，优先显示字母
      const specialChar = SpecialCoordinateService.getCharacterForCoordinate(cell.x, cell.y);
      if (specialChar) {
        // 黑色格子始终显示字母，不管显示模式
        return specialChar;
      }

      switch (displayMode) {
        case 'coordinates':
          return `${cell.x},${cell.y}`;

        case 'value':
          const coordKey = `${cell.x},${cell.y}`;
          const dataPoint = coordinateMap.get(coordKey);

          if (dataPoint) {
            return ColorMappingService.getDisplayValue(dataPoint.color, dataPoint.level);
          }

          return null;

        case 'color':
          return null;

        default:
          return null;
      }
    } catch (error) {
      return null;
    }
  }, [coordinateMap, isHydrated]);

  // 生成网格数据 - 专注于数据结构，不处理渲染逻辑
  const cells = useMemo((): CellData[][] => {
    if (!isHydrated) {
      return Array(33).fill(null).map(() => Array(33).fill(null));
    }

    try {
      const grid: CellData[][] = [];
      const gridSize = 33;
      const center = Math.floor(gridSize / 2);

      for (let row = 0; row < gridSize; row++) {
        const gridRow: CellData[] = [];

        for (let col = 0; col < gridSize; col++) {
          const x = col - center;
          const y = center - row;

          // 基础单元格数据，不包含渲染相关的颜色计算
          const cellData: CellData = {
            id: `cell-${x}-${y}`,
            row,
            col,
            x,
            y,
            index: row * gridSize + col,
            color: 'transparent', // 默认透明，具体颜色由渲染引擎计算
            colorMappingValue: 0,
            level: 1,
            group: null,
            isActive: isCellActive(x, y),
            number: row * gridSize + col,
          };

          // 设置数据属性（非渲染属性）
          const coordKey = `${x},${y}`;
          const dataPoint = coordinateMap.get(coordKey);
          if (dataPoint) {
            cellData.colorMappingValue = ColorMappingService.getNumericValue(dataPoint.color, dataPoint.level);
            cellData.level = dataPoint.level;
            cellData.group = dataPoint.group; // 直接使用字母组
          } else {
            const specialChar = SpecialCoordinateService.getCharacterForCoordinate(x, y);
            if (specialChar) {
              cellData.colorMappingValue = SpecialCoordinateService.getNumericValueForCharacter(specialChar);
            }
          }

          gridRow.push(cellData);
        }

        grid.push(gridRow);
      }

      return grid;
    } catch (error) {
      setError(error as Error);
      return Array(33).fill(null).map(() => Array(33).fill(null));
    }
  }, [coordinateMap, colorValues, colorVisibility, groupVisibility, isHydrated, isCellActive]); // 移除getCellColor依赖

  // 刷新数据
  const refreshData = useCallback((): void => {
    try {
      regenerateMatrixData();
    } catch (error) {
      setError(error as Error);
    }
  }, [regenerateMatrixData]);

  // 改进的loading状态判断：确保数据完整性
  const isDataReady = isHydrated && matrixData && matrixData.byCoordinate.size > 0;
  const isLoadingState = !isDataReady;

  return {
    cells,
    isLoading: isLoadingState,
    error,
    refreshData,
    isCellActive,
    getCellContent,
  };
};
