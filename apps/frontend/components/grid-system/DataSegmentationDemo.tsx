/**
 * 数据分割优化演示组件
 * 🎯 核心价值：展示不同数据模式的性能差异和优化效果
 * 📦 功能范围：可视化数据生成时间、内存使用和模式切换
 * ⚡ 演示功能：实时性能对比、模式切换测试
 */

'use client';

import React, { useState, useCallback, useRef } from 'react';
import { globalModeAwareGenerator } from '@/lib/utils/modeAwareDataGenerator';
import { DataMode, type DataGenerationConfig, type GridDataSet } from '@/lib/types/grid-data-modes';

interface PerformanceMetrics {
  mode: DataMode;
  generationTime: number;
  memoryEstimate: string;
  cellCount: number;
  timestamp: number;
}

interface DemoState {
  currentMode: DataMode;
  isGenerating: boolean;
  metrics: PerformanceMetrics[];
  lastResult: GridDataSet | null;
  error: string | null;
}

export default function DataSegmentationDemo() {
  const [state, setState] = useState<DemoState>({
    currentMode: DataMode.COORDINATE_ONLY,
    isGenerating: false,
    metrics: [],
    lastResult: null,
    error: null
  });

  const metricsRef = useRef<PerformanceMetrics[]>([]);

  /**
   * 生成指定模式的数据并记录性能指标
   */
  const generateData = useCallback(async (mode: DataMode) => {
    setState(prev => ({ ...prev, isGenerating: true, error: null }));

    try {
      const startTime = performance.now();
      
      const config: DataGenerationConfig = {
        mode,
        enableCache: false, // 禁用缓存以获得准确的生成时间
        enableSpecialCharacters: true,
        gridSize: 33
      };

      const result = globalModeAwareGenerator.generateData(config);
      const endTime = performance.now();
      const generationTime = endTime - startTime;

      // 估算内存使用
      const jsonString = JSON.stringify(result);
      const memoryBytes = new Blob([jsonString]).size;
      const memoryEstimate = `${(memoryBytes / 1024).toFixed(2)}KB`;

      const metrics: PerformanceMetrics = {
        mode,
        generationTime,
        memoryEstimate,
        cellCount: result.totalCells,
        timestamp: Date.now()
      };

      metricsRef.current = [...metricsRef.current.slice(-9), metrics]; // 保留最近10条记录

      setState(prev => ({
        ...prev,
        currentMode: mode,
        isGenerating: false,
        metrics: metricsRef.current,
        lastResult: result,
        error: null
      }));

      console.log(`🎯 [DataSegmentationDemo] ${mode} 数据生成完成:`, metrics);

    } catch (error) {
      console.error('数据生成失败:', error);
      setState(prev => ({
        ...prev,
        isGenerating: false,
        error: error instanceof Error ? error.message : '未知错误'
      }));
    }
  }, []);

  /**
   * 运行所有模式的性能对比测试
   */
  const runPerformanceComparison = useCallback(async () => {
    const modes = [DataMode.COORDINATE_ONLY, DataMode.BASIC_RENDER, DataMode.FULL_RENDER];
    
    for (const mode of modes) {
      await generateData(mode);
      // 添加小延迟以便观察过程
      await new Promise(resolve => setTimeout(resolve, 500));
    }
  }, [generateData]);

  /**
   * 清除性能记录
   */
  const clearMetrics = useCallback(() => {
    metricsRef.current = [];
    setState(prev => ({
      ...prev,
      metrics: [],
      lastResult: null,
      error: null
    }));
  }, []);

  /**
   * 获取性能改进百分比
   */
  const getPerformanceImprovement = useCallback((mode: DataMode): string => {
    const fullRenderMetrics = state.metrics.find(m => m.mode === DataMode.FULL_RENDER);
    const currentMetrics = state.metrics.find(m => m.mode === mode);
    
    if (!fullRenderMetrics || !currentMetrics) return 'N/A';
    
    const improvement = ((fullRenderMetrics.generationTime - currentMetrics.generationTime) / fullRenderMetrics.generationTime * 100);
    return improvement > 0 ? `+${improvement.toFixed(1)}%` : `${improvement.toFixed(1)}%`;
  }, [state.metrics]);

  /**
   * 获取模式显示名称
   */
  const getModeDisplayName = (mode: DataMode): string => {
    switch (mode) {
      case DataMode.COORDINATE_ONLY:
        return '坐标模式';
      case DataMode.BASIC_RENDER:
        return '基础渲染';
      case DataMode.FULL_RENDER:
        return '完整渲染';
      default:
        return mode;
    }
  };

  /**
   * 获取模式描述
   */
  const getModeDescription = (mode: DataMode): string => {
    switch (mode) {
      case DataMode.COORDINATE_ONLY:
        return '仅包含坐标和特殊字符信息，最轻量级';
      case DataMode.BASIC_RENDER:
        return '包含坐标、颜色映射值和等级信息';
      case DataMode.FULL_RENDER:
        return '包含所有渲染所需的完整信息';
      default:
        return '';
    }
  };

  return (
    <div className="p-6 bg-white rounded-lg shadow-lg">
      <div className="mb-6">
        <h2 className="text-2xl font-bold text-gray-800 mb-2">
          🎯 数据分割优化演示
        </h2>
        <p className="text-gray-600">
          展示不同数据模式的性能差异，验证数据分割优化效果
        </p>
      </div>

      {/* 控制面板 */}
      <div className="mb-6 p-4 bg-gray-50 rounded-lg">
        <h3 className="text-lg font-semibold mb-3">控制面板</h3>
        <div className="flex flex-wrap gap-3">
          {Object.values(DataMode).map((mode) => (
            <button
              key={mode}
              onClick={() => generateData(mode)}
              disabled={state.isGenerating}
              className={`px-4 py-2 rounded-md font-medium transition-colors ${
                state.currentMode === mode
                  ? 'bg-blue-500 text-white'
                  : 'bg-white text-gray-700 border border-gray-300 hover:bg-gray-50'
              } ${state.isGenerating ? 'opacity-50 cursor-not-allowed' : 'cursor-pointer'}`}
            >
              {getModeDisplayName(mode)}
            </button>
          ))}
          <button
            onClick={runPerformanceComparison}
            disabled={state.isGenerating}
            className="px-4 py-2 bg-green-500 text-white rounded-md font-medium hover:bg-green-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            运行性能对比
          </button>
          <button
            onClick={clearMetrics}
            disabled={state.isGenerating}
            className="px-4 py-2 bg-red-500 text-white rounded-md font-medium hover:bg-red-600 transition-colors disabled:opacity-50 disabled:cursor-not-allowed"
          >
            清除记录
          </button>
        </div>
      </div>

      {/* 加载状态 */}
      {state.isGenerating && (
        <div className="mb-4 p-3 bg-blue-50 border border-blue-200 rounded-lg">
          <div className="flex items-center">
            <div className="animate-spin rounded-full h-4 w-4 border-b-2 border-blue-500 mr-2"></div>
            <span className="text-blue-700">正在生成 {getModeDisplayName(state.currentMode)} 数据...</span>
          </div>
        </div>
      )}

      {/* 错误信息 */}
      {state.error && (
        <div className="mb-4 p-3 bg-red-50 border border-red-200 rounded-lg">
          <span className="text-red-700">错误: {state.error}</span>
        </div>
      )}

      {/* 性能指标表格 */}
      {state.metrics.length > 0 && (
        <div className="mb-6">
          <h3 className="text-lg font-semibold mb-3">性能指标对比</h3>
          <div className="overflow-x-auto">
            <table className="w-full border-collapse border border-gray-300">
              <thead>
                <tr className="bg-gray-100">
                  <th className="border border-gray-300 px-4 py-2 text-left">数据模式</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">生成时间</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">内存使用</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">单元格数</th>
                  <th className="border border-gray-300 px-4 py-2 text-left">性能提升</th>
                </tr>
              </thead>
              <tbody>
                {state.metrics.map((metric, index) => (
                  <tr key={index} className={metric.mode === state.currentMode ? 'bg-blue-50' : ''}>
                    <td className="border border-gray-300 px-4 py-2">
                      <div>
                        <div className="font-medium">{getModeDisplayName(metric.mode)}</div>
                        <div className="text-sm text-gray-500">{getModeDescription(metric.mode)}</div>
                      </div>
                    </td>
                    <td className="border border-gray-300 px-4 py-2 font-mono">
                      {metric.generationTime.toFixed(2)}ms
                    </td>
                    <td className="border border-gray-300 px-4 py-2 font-mono">
                      {metric.memoryEstimate}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      {metric.cellCount.toLocaleString()}
                    </td>
                    <td className="border border-gray-300 px-4 py-2">
                      <span className={`font-medium ${
                        getPerformanceImprovement(metric.mode).startsWith('+') 
                          ? 'text-green-600' 
                          : 'text-gray-600'
                      }`}>
                        {getPerformanceImprovement(metric.mode)}
                      </span>
                    </td>
                  </tr>
                ))}
              </tbody>
            </table>
          </div>
        </div>
      )}

      {/* 当前数据信息 */}
      {state.lastResult && (
        <div className="p-4 bg-gray-50 rounded-lg">
          <h3 className="text-lg font-semibold mb-3">当前数据信息</h3>
          <div className="grid grid-cols-2 md:grid-cols-4 gap-4 text-sm">
            <div>
              <span className="font-medium text-gray-600">模式:</span>
              <div className="text-gray-800">{getModeDisplayName(state.lastResult.mode)}</div>
            </div>
            <div>
              <span className="font-medium text-gray-600">单元格数:</span>
              <div className="text-gray-800">{state.lastResult.totalCells.toLocaleString()}</div>
            </div>
            <div>
              <span className="font-medium text-gray-600">特殊字符:</span>
              <div className="text-gray-800">
                {'specialCharacters' in state.lastResult 
                  ? state.lastResult.specialCharacters.size 
                  : 'N/A'}
              </div>
            </div>
            <div>
              <span className="font-medium text-gray-600">数据结构:</span>
              <div className="text-gray-800">
                {state.lastResult.mode === DataMode.COORDINATE_ONLY && '轻量级坐标'}
                {state.lastResult.mode === DataMode.BASIC_RENDER && '基础渲染'}
                {state.lastResult.mode === DataMode.FULL_RENDER && '完整渲染'}
              </div>
            </div>
          </div>
        </div>
      )}
    </div>
  );
}
