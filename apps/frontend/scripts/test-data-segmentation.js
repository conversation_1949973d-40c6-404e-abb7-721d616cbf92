#!/usr/bin/env node

/**
 * 数据分割优化测试脚本
 * 🎯 核心价值：验证模式感知数据生成的性能提升效果
 * 📦 功能范围：对比不同数据模式的生成时间和内存使用
 * ⚡ 性能测试：坐标模式 vs 数值模式 vs 颜色模式
 */

const { performance } = require('perf_hooks');

// 模拟数据模式枚举
const DataMode = {
  COORDINATE_ONLY: 'coordinate-only',
  BASIC_RENDER: 'basic-render',
  FULL_RENDER: 'full-render'
};

// 模拟网格大小
const GRID_SIZE = 33;
const TOTAL_CELLS = GRID_SIZE * GRID_SIZE;

/**
 * 模拟坐标模式数据生成
 */
function generateCoordinateOnlyData() {
  const startTime = performance.now();
  const cells = [];
  const centerOffset = Math.floor(GRID_SIZE / 2);

  for (let row = 0; row < GRID_SIZE; row++) {
    for (let col = 0; col < GRID_SIZE; col++) {
      const x = col - centerOffset;
      const y = centerOffset - row;
      
      cells.push({
        id: `cell-${row * GRID_SIZE + col}`,
        row,
        col,
        x,
        y,
        index: row * GRID_SIZE + col,
        isActive: true
        // 注意：没有颜色、等级、组等复杂数据
      });
    }
  }

  const endTime = performance.now();
  return {
    mode: DataMode.COORDINATE_ONLY,
    cells,
    totalCells: cells.length,
    generationTime: endTime - startTime,
    memoryEstimate: estimateMemoryUsage(cells, 'coordinate')
  };
}

/**
 * 模拟基础渲染数据生成
 */
function generateBasicRenderData() {
  const startTime = performance.now();
  const cells = [];
  const coordinateMap = new Map();
  const centerOffset = Math.floor(GRID_SIZE / 2);

  for (let row = 0; row < GRID_SIZE; row++) {
    for (let col = 0; col < GRID_SIZE; col++) {
      const x = col - centerOffset;
      const y = centerOffset - row;
      const cellId = row * GRID_SIZE + col;
      
      // 模拟颜色映射值计算
      const colorMappingValue = Math.floor(Math.random() * 100);
      const level = Math.floor(Math.random() * 4) + 1;
      
      cells.push({
        id: `cell-${cellId}`,
        row,
        col,
        x,
        y,
        index: cellId,
        colorMappingValue,
        level,
        color: `hsl(${colorMappingValue * 3.6}, 70%, 50%)`,
        isActive: true
      });

      coordinateMap.set(`${x},${y}`, { colorMappingValue, level });
    }
  }

  const endTime = performance.now();
  return {
    mode: DataMode.BASIC_RENDER,
    cells,
    coordinateMap,
    totalCells: cells.length,
    generationTime: endTime - startTime,
    memoryEstimate: estimateMemoryUsage(cells, 'basic')
  };
}

/**
 * 模拟完整渲染数据生成
 */
function generateFullRenderData() {
  const startTime = performance.now();
  const cells = [];
  const coordinateMap = new Map();
  const matrixData = new Map(); // 模拟完整矩阵数据
  const centerOffset = Math.floor(GRID_SIZE / 2);

  // 模拟复杂的矩阵数据生成
  for (let i = 0; i < 1000; i++) {
    matrixData.set(`data-${i}`, {
      id: i,
      color: ['red', 'blue', 'green', 'yellow'][Math.floor(Math.random() * 4)],
      level: Math.floor(Math.random() * 4) + 1,
      group: ['A', 'B', 'C', 'D', 'E'][Math.floor(Math.random() * 5)],
      transformRule: `rule-${i}`
    });
  }

  for (let row = 0; row < GRID_SIZE; row++) {
    for (let col = 0; col < GRID_SIZE; col++) {
      const x = col - centerOffset;
      const y = centerOffset - row;
      const cellId = row * GRID_SIZE + col;
      
      // 模拟复杂的数据计算
      const colorMappingValue = Math.floor(Math.random() * 100);
      const level = Math.floor(Math.random() * 4) + 1;
      const colorType = ['red', 'blue', 'green', 'yellow'][Math.floor(Math.random() * 4)];
      const group = ['A', 'B', 'C', 'D', 'E'][Math.floor(Math.random() * 5)];
      
      cells.push({
        id: `cell-${cellId}`,
        row,
        col,
        x,
        y,
        index: cellId,
        color: `hsl(${colorMappingValue * 3.6}, 70%, 50%)`,
        colorMappingValue,
        colorType,
        level,
        group,
        isActive: true,
        number: cellId + 1,
        // 额外的复杂字段
        metadata: {
          created: new Date(),
          rules: [`rule-${cellId}`, `rule-${cellId + 1}`],
          properties: {
            weight: Math.random(),
            priority: Math.floor(Math.random() * 10)
          }
        }
      });

      coordinateMap.set(`${x},${y}`, { color: colorType, level, group });
    }
  }

  const endTime = performance.now();
  return {
    mode: DataMode.FULL_RENDER,
    cells,
    coordinateMap,
    matrixData,
    totalCells: cells.length,
    generationTime: endTime - startTime,
    memoryEstimate: estimateMemoryUsage(cells, 'full')
  };
}

/**
 * 估算内存使用量
 */
function estimateMemoryUsage(data, type) {
  const jsonString = JSON.stringify(data);
  const bytes = new Blob([jsonString]).size;
  
  return {
    bytes,
    kb: (bytes / 1024).toFixed(2),
    mb: (bytes / (1024 * 1024)).toFixed(3),
    type
  };
}

/**
 * 运行性能测试
 */
function runPerformanceTest() {
  console.log('🚀 开始数据分割优化性能测试\n');
  console.log(`📊 测试配置: ${GRID_SIZE}x${GRID_SIZE} 网格 (${TOTAL_CELLS} 个单元格)\n`);

  const results = [];

  // 测试坐标模式
  console.log('🎯 测试坐标模式数据生成...');
  const coordinateResult = generateCoordinateOnlyData();
  results.push(coordinateResult);
  console.log(`   ✅ 完成: ${coordinateResult.generationTime.toFixed(2)}ms, ${coordinateResult.memoryEstimate.kb}KB\n`);

  // 测试基础渲染模式
  console.log('🎯 测试基础渲染模式数据生成...');
  const basicResult = generateBasicRenderData();
  results.push(basicResult);
  console.log(`   ✅ 完成: ${basicResult.generationTime.toFixed(2)}ms, ${basicResult.memoryEstimate.kb}KB\n`);

  // 测试完整渲染模式
  console.log('🎯 测试完整渲染模式数据生成...');
  const fullResult = generateFullRenderData();
  results.push(fullResult);
  console.log(`   ✅ 完成: ${fullResult.generationTime.toFixed(2)}ms, ${fullResult.memoryEstimate.kb}KB\n`);

  // 性能对比分析
  console.log('📈 性能对比分析:');
  console.log('=' .repeat(80));
  console.log('模式'.padEnd(20) + '生成时间'.padEnd(15) + '内存使用'.padEnd(15) + '相对性能');
  console.log('-'.repeat(80));

  const baseTime = fullResult.generationTime;
  const baseMemory = fullResult.memoryEstimate.bytes;

  results.forEach(result => {
    const timeImprovement = ((baseTime - result.generationTime) / baseTime * 100).toFixed(1);
    const memoryImprovement = ((baseMemory - result.memoryEstimate.bytes) / baseMemory * 100).toFixed(1);
    
    console.log(
      result.mode.padEnd(20) + 
      `${result.generationTime.toFixed(2)}ms`.padEnd(15) + 
      `${result.memoryEstimate.kb}KB`.padEnd(15) + 
      `时间: ${timeImprovement > 0 ? '+' : ''}${timeImprovement}%, 内存: ${memoryImprovement > 0 ? '+' : ''}${memoryImprovement}%`
    );
  });

  console.log('=' .repeat(80));

  // 优化建议
  console.log('\n💡 优化效果分析:');
  const coordinateImprovement = ((baseTime - coordinateResult.generationTime) / baseTime * 100).toFixed(1);
  const coordinateMemoryImprovement = ((baseMemory - coordinateResult.memoryEstimate.bytes) / baseMemory * 100).toFixed(1);
  
  console.log(`   🎯 坐标模式优化: 生成时间减少 ${coordinateImprovement}%, 内存使用减少 ${coordinateMemoryImprovement}%`);
  console.log(`   📊 预期用户体验提升: 坐标模式下数据加载速度提升 ${coordinateImprovement}%`);
  console.log(`   🔧 建议: 在坐标显示模式下使用轻量级数据结构，避免不必要的颜色和等级计算`);

  return results;
}

// 运行测试
if (require.main === module) {
  runPerformanceTest();
}

module.exports = {
  runPerformanceTest,
  generateCoordinateOnlyData,
  generateBasicRenderData,
  generateFullRenderData,
  DataMode
};
